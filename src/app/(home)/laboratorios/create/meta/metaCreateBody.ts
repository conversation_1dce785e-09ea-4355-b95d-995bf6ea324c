
import { FieldMeta } from "@/_core/ui/components/forms/types";
import { InferSchema } from "@/_lib/data/model/schema";

export const metaCreateBody: Record<
  keyof InferSchema<"laboratorios", "create">,
  FieldMeta
> = {
    razonSocial: {
    label: "Razón social",
    type: "text",
    grid: { xs: 12, md: 12, lg: 12, xl: 12 },
  },
  estadoId: {
    label: "Estado",
    type: "asyncSelect",
    entity: "estados_laboratorios",
    labelKeys: ["nombre"],
    valueKey: "id",
    grid: { xs: 12, md: 12, lg: 12, xl: 12 },
  },
  domicilio: {
    type: "group",
    label: "<PERSON><PERSON><PERSON>",
    fields: {
      calle: {
        label: "Calle",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      numero: {
        label: "Número",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      piso: {
        label: "Piso (opcional)",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      depto: {
        label: "Dpto (opcional)",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      descripcion: {
        label: "Descripción (opcional)",
        type: "text",
        grid: { xs: 12, md: 12, lg: 12, xl: 12 },
      },
      localidadId: {
        label: "Localidad",
        type: "asyncSelect",
        entity: "localidades",
        labelKeys: ["codigoPostal", "nombre", "partido.nombre"],
        valueKey: "id",
        grid: { xs: 12, md: 12, lg: 12, xl: 12 },
      },
      telefonoCodigoArea: {
        label: "Código de área",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      telefonoNumero: {
        label: "Número de teléfono",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      email: {
        label: "Email",
        type: "text",
        grid: { xs: 12, md: 12, lg: 12, xl: 12 },
      },
    },
  },
};
