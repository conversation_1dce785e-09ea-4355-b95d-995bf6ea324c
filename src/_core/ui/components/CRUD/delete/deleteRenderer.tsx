
"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  IconButton
} from "@mui/material";
import { Delete, X, AlertTriangle } from "react-feather";
import { useMutation, useQueryClient } from "@tanstack/react-query";

import { EntityKeys } from "@/_lib/utils/entities";
import { runFormAction } from "@/_lib/data/model/action/actionFactory";
import { showErrorToast } from "@/_core/ui/components/toaster/toast";

interface DeleteRendererProps {
  entityName: EntityKeys;
  entityId: string;
  entityLabel?: string;
  onDeleteSuccess?: () => void;
}

export default function DeleteRenderer({
  entityName,
  entityId,
  entityLabel,
  onDeleteSuccess,
}: DeleteRendererProps) {
  const [open, setOpen] = useState(false);
  const queryClient = useQueryClient();

  const deleteMutation = useMutation({
    mutationFn: async () => {
      await runFormAction(entityName, "delete", {
        params: { id: entityId }
      });
    },
    onSuccess: () => {
      // Invalidar queries relacionadas para refrescar los datos
      queryClient.invalidateQueries({
        queryKey: [entityName]
      });
      setOpen(false);
      onDeleteSuccess?.();
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message ||
                    error?.message ||
                    "Error al eliminar el elemento";
      showErrorToast(message);
    },
  });

  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  const handleConfirmDelete = () => {
    deleteMutation.mutate();
  };

  return (
    <>
      {/* Trigger Button */}
      <Stack
        direction="row"
        alignItems="center"
        spacing={2}
        onClick={handleOpen}
        sx={{ cursor: "pointer" }}
      >
        <Delete />
        <Typography
          sx={{ typography: { xs: "body2", md: "body1xl", lg: "body1xl" } }}
        >
          Eliminar
        </Typography>
      </Stack>

      {/* Confirmation Modal */}
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
        sx={{
          "& .MuiDialog-paper": {
            borderRadius: 2,
            p: 1
          }
        }}
      >
        <DialogTitle>
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Stack direction="row" alignItems="center" spacing={2}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  width: 40,
                  height: 40,
                  borderRadius: "50%",
                  bgcolor: "error.light",
                  color: "error.main"
                }}
              >
                <AlertTriangle size={20} />
              </Box>
              <Typography variant="h6" color="text.primary">
                Confirmar eliminación
              </Typography>
            </Stack>
            <IconButton onClick={handleClose} size="small">
              <X size={20} />
            </IconButton>
          </Stack>
        </DialogTitle>

        <DialogContent>
          <Typography color="text.secondary" sx={{ mb: 2 }}>
            ¿Estás seguro de que deseas eliminar {entityLabel ? `"${entityLabel}"` : "este elemento"}?
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Esta acción no se puede deshacer.
          </Typography>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={handleClose}
            variant="outlined"
            color="inherit"
            disabled={deleteMutation.isPending}
          >
            Cancelar
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
            startIcon={deleteMutation.isPending ? undefined : <Delete size={16} />}
          >
            {deleteMutation.isPending ? "Eliminando..." : "Eliminar"}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
