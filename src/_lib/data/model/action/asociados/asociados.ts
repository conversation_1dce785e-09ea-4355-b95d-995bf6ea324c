'server-only';

import { validateApiInput } from "@/_core/lib/service/validationService";
import { CollectionHttpRequestBuilder, CreateHttpRequestBuilder, IndexHttpRequestBuilder } from "@/_core/lib/data/action/builder";
import { AsociadosInterface } from "@/_lib/data/model/interface/";

import { getEntityConfig } from "@/_lib/utils/entities";

import { ServerActionParams } from "../actionFactory";
import { BadRequestError } from "@/_core/lib/context/error";

import { redirect } from "next/navigation";

export async function submitAsociadosCollection( { query = {} }: ServerActionParams ) {
  "use server";

  const { meta } = getEntityConfig("asociados", "collection");

  const builder = new CollectionHttpRequestBuilder(meta.url)
    .setQueryParams(query)
    .withValidation(AsociadosInterface.collectionAsociadosModel)
    /*
    .addAfterExecute((response) => {
      const result = validateApiInput(
        AsociadosInterface.collectionAsociadosModel,
        "response",
        response.data
      );
      response.data = result;
    });
    */

  const response = await builder.run();
  return response.data;
}

export async function submitAsociadosIndex( { params = {} }: ServerActionParams ) {
  "use server";

  const { meta } = getEntityConfig("asociados", "index");

  const { id } = params;
  if (!id) {
    throw new BadRequestError( "La petición no contiene un id.");
  }

  const builder = new IndexHttpRequestBuilder(meta.url.replace(":id", id.toString()))
    /*
    .withValidation(AsociadosInterface.indexAsociadosModel)
    .addAfterExecute((response) => {
      const result = validateApiInput(
        AsociadosInterface.indexAsociadosModel,
        "response",
        response.data
      );
      response.data = result;
    });
    */

  const response = await builder.run();
  return response.data;
}

export async function submitAsociadosCreate( params: ServerActionParams ) {
  "use server";

  const { meta } = getEntityConfig("asociados", "create");

  const { body } = params;
  if (!body) {
    throw new BadRequestError( "La petición no contiene un body.");
  }


  const builder = new CreateHttpRequestBuilder(meta.url)
    .setBody(body)
    .withValidation(AsociadosInterface.createAsociadosModel)
    .addAfterExecute((response) => {
      const result = validateApiInput(
        AsociadosInterface.createAsociadosModel,
        "response",
        response.data
      );
      response.data = result;
    })
    .addAfterExecute(() => {
      redirect("/asociados");
    });

  const response = await builder.run();
  return response.data;
}
