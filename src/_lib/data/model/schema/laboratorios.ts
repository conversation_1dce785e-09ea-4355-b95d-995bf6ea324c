
import { z } from "zod";
import { ENTITIES } from "@/_lib/config/entities";

export const ENTITY = ENTITIES.laboratorios;

export const laboratoriosSchemas = {
  index: {
    response: z.object({
      id: z.string().uuid(),
    }),
  },
  collection: {
    filters: z.object({
      search: z.string().optional().nullable(),
      page: z.number().int().min(1).default(1).optional().nullable(),
      limit: z.number().int().min(1).max(30).default(30).optional().nullable(),
    }),
    response: z.object({
        id: z.number().int(),
        razonSocial: z.string(),
        codigo: z.string(),
        cuit: z.string(),
        iva: z.string(),
        domicilio: z.object({
          calle: z.string(),
          numero: z.string(),
          localidad: z.object({
            nombre: z.string(),
          }),
        }),
    }),
  },
  create: {
    body: z.object({
      razonSocial: z
        .string()
        .min(3, "Ingrese por los menos 3 caracteres")
        .max(20, "No puede superar los 20 caracteres"),
      estadoId: z.number().int(),
      domicilio: z.object({
        calle: z
          .string()
          .min(3, "Ingrese por los menos 3 caracteres")
          .max(20, "No puede superar los 20 caracteres"),
        numero: z
          .string()
          .min(3, "Ingrese por los menos 3 caracteres")
          .max(20, "No puede superar los 20 caracteres"),
        piso: z.string().optional().nullable(),
        depto: z.string().optional().nullable(),
        descripcion: z.string().optional().nullable(),
        telefonoCodigoArea: z
          .string({
            errorMap: () => ({
              message: "El código de área es obligatorio",
            }),
          })
          .regex(
            /^\d{2,4}$/,
            "El código de área debe contener entre 2 y 4 dígitos"
          ),
        telefonoNumero: z
          .string({
            errorMap: () => ({
              message: "El número de teléfono es obligatorio",
            }),
          })
          .regex(
            /^\d{6,8}$/,
            "El número de teléfono debe contener entre 6 y 8 dígitos"
          ),
        email: z.string().email("El email debe ser un email válido"),
        localidadId: z.number().int(),
      }),
    }),
    response: z.object({
      id: z.number().int(),
    }),
  },
} as const;

