import { ApiModelCollectionBuilder, ApiModelCreateBuilder } from "@/_core/lib/data/interface/builder";
import { ENTITIES } from "@/_lib/config/entities";
import { getEntityKey, getActionKeys } from "@/_lib/utils/entities";
import { asociadosSchemas } from "../schema/asociados";

const { asociados } = ENTITIES;

// Nombre de la entidad
export const NAME = getEntityKey(asociados) as string;

// Acciones disponibles en la entidad
export const ACTION_NAMES = getActionKeys(asociados) as (keyof typeof asociadosSchemas)[];

// Modelos generados
export const collectionAsociadosModel = new ApiModelCollectionBuilder(ACTION_NAMES[0])
  .setFilters(asociadosSchemas.collection.filters)
  .setResponseItemsSchema(asociadosSchemas.collection.response)
  .build();

export const indexAsociadosModel = new ApiModelCollectionBuilder(ACTION_NAMES[1])
  .setResponse(asociadosSchemas.index.response)
  .build();

export const createAsociadosModel = new ApiModelCreateBuilder(ACTION_NAMES[2])
  .setBody(asociadosSchemas.create.body)
  .setResponse(asociadosSchemas.create.response)
  .build();
